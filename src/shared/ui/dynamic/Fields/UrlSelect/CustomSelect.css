.dropdown {
    position: relative;
}
.dropdown__button {
    position: relative;
    display: block;
    width: 100%;
    text-align: left;
    background: transparent;
    border: 1px solid #EEEEEE;
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.6);
    height: 46px;
    padding: 15px 10px;
    cursor: pointer;
}

.error {
    position: relative;
    display: block;
    width: 100%;
    text-align: left;
    background: transparent;
    border: 1px solid #FF4C41;
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.6);
    height: 46px;
    padding: 15px 10px;
    cursor: pointer;
}

.dropdown__button::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);

    /* Рисуем треугольник */
    width: 0;
    height: 0;
    border-width: 10.4px 6px 0 6px;
    border-color: #EEEEEE;
    border-style: solid;

    /* Сни<PERSON><PERSON><PERSON><PERSON> клик с треугольника */
    pointer-events: none;

}

.dropdown__list {
    display: none;

    position: absolute;
    left: 0;
    top: 30px;

    margin: 0;
    padding: 0;
    list-style-type: none;
    box-shadow: 0px 4px 8px rgba(176, 198, 225, 0.6);
    overflow-x: hidden;
    overflow-y: scroll;
    max-height: 30vh;

    border-radius: 6px;
    width: 100%;

    z-index: 1;
}

.dropdown__dark {
    background: #3B3363;
}

.dropdown__light {
    background: #fff;
}

.dropdown__list--visible {
    display: block;
}

.dropdown__button:focus, .dropdown__button--active {
    outline: none;
    box-shadow: 0px 0px 0px 4px rgba(176, 198, 225, 0.6);
}

.dropdown__button::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);

    /* Рисуем треугольник */
    width: 0;
    height: 0;
    border-width: 10.4px 6px 0 6px;
    border-color: #EEEEEE transparent transparent transparent;
    border-style: solid;

    /* Снимаем клик с треугольника */
    pointer-events: none;
}

.dropdown__list-item {
    margin: 0;
    border: 1px solid #EEEEEE;
    border-bottom: 0px;
    padding: 13px 20px;
    cursor: pointer;
}

.dropdown__list-item:first-child {
    border-radius: 6px 6px 0 0;
}

.dropdown__list-item:last-child {
    border-radius: 0 0 6px 6px;
    border-bottom: 1px solid #7E9BBD;
}

.dropdown__list-item:hover {
    background: rgba(176, 198, 225, 0.26);
}

.dropdown__list-item-active {
    color: white;
    background: rgba(15, 27, 49, 0.46);
}

.dropdown__input-hidden {
    display: none;
}