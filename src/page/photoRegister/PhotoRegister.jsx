import { useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import Webcam from 'react-webcam';
import TextField from 'shared/ui/dynamic/Fields/TextField';
import { useDispatch, useSelector } from 'react-redux';
import { uploadPhoto } from 'shared/store/reducer/person.reducer';


const PhotoRegister = () => {
    const canvas = useRef();
    const stream = useRef();
    const photo = useRef();
    const requestPhoto = useRef();
    const shotPhoto = () => {
        const photoCanvas = document.createElement("canvas");
        photoCanvas.style.display = "none";
        const ctx = photoCanvas.getContext("2d");
        photoCanvas.width = stream.current.video.videoWidth;
        photoCanvas.height = stream.current.video.videoHeight;
        ctx.drawImage(stream.current.video, 0, 0, stream.current.video.videoWidth, stream.current.video.videoHeight);
        // photo.current.src = dataURL;
        // photo.current.display = "block";
        requestPhoto.current = photoCanvas.toDataURL('image/jpeg');
    }
    const [show, setShow] = useState(false);
    const [isEnv,setIsEnv] = useState(true);    

    const videoConstraints = {
        facingMode: isEnv?"environment":"user",
    };

    const methods = useForm();
    const dispatch = useDispatch();
    const person = useSelector(({ person }) => person);
    const onSubmit = async (body) => {
        try {
            shotPhoto()
            setShow(true);
            const label = body.firstname || Math.random().toString(36).substring(2, 22)            
            dispatch(uploadPhoto({ photo: requestPhoto.current.substring(22), name: label }));
        } catch (e) {
            console.log(e);
        }
    }
    // console.log(person);
    return <div className="flex flex-col md:flex-row justify-center items-center">
        <form className="p-5" onSubmit={methods.handleSubmit(onSubmit)}>
            <FormProvider {...methods}>
                <TextField label="ФИО" name="firstname" />
                <div className="mt-3 flex flex-col md:flex-row">
                    <button disabled={person?.isLoading} type="submit"
                        className={`inline-flex items-center gap-x-2 rounded-md bg-transparent px-3.5 py-2.5 text-sm font-semibold text-gray-700 shadow-sm hover:bg-blue-400 hover:text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent disabled:hover:text-gray-700`}                    >
                        Отправить
                    </button>
                    <button disabled={person?.isLoading} onClick={()=>{setIsEnv(v=>!v)}} type="button"
                        className={`inline-flex items-center gap-x-2 rounded-md bg-transparent px-3.5 py-2.5 text-sm font-semibold text-gray-700 shadow-sm hover:bg-blue-400 hover:text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent disabled:hover:text-gray-700`}                    >
                        Поменять камеру
                    </button>
                </div>
            </FormProvider>
        </form>
        <div className="flex items-center  ">
            <div className="w-full max-w-md p-4 bg-white shadow-lg rounded-lg">
                <div className="relative" onClick={() => shotPhoto()}>
                    <Webcam ref={stream} videoConstraints={videoConstraints} className="w-full h-auto rounded-lg" />
                    <canvas ref={canvas} width={320} height={240} className="absolute top-0 left-0 w-full h-auto rounded-lg" />
                </div>
                <div className="mr-auto mb-4 md:mb-0">
                    <span className="m-10 text-red-500 block">
                        {photo.current?.src != "#" && show && (!person?.isLoading && person?.error?.message?.ru)}
                    </span>
                    <span className="m-10 text-red-500 block">
                        {photo.current?.src != "#" && show && (!person?.isLoading && person?.error && `Accuracy: ${person?.error?.accuracy}`)}
                    </span>
                    <span className="m-10 text-green-500 block">
                        {photo.current?.src != "#" && show && !person?.isLoading && !person?.error && "Вы прошли проверку"}
                    </span>
                    <span className="m-10 text-green-500 block">
                        {photo.current?.src != "#" && show && !person?.isLoading && !person?.error && `Accuracy: ${person?.photo?.similarity} `}
                    </span>
                    <span className="m-10 block">
                        {photo.current?.src != "#" && show && (person?.isLoading && ("...Загрузка..."))}
                    </span>
                </div>
            </div>
        </div>
    </div>
}

export default PhotoRegister;